{% extends 'JW/base.html' %}

{% block title %}Statistics{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <!-- Overall Statistics -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Total Lessons</h5>
                    <h2 class="card-text">{{ lesson_stats.total_lessons }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Total Hours</h5>
                    <h2 class="card-text">{{ lesson_stats.total_hours|floatformat:1 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Total Income</h5>
                    <h2 class="card-text">£{{ lesson_stats.total_income|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Total Expenses</h5>
                    <h2 class="card-text">£{{ lesson_stats.total_expenses|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Gross</h5>
                    <h2 class="card-text">£{{ lesson_stats.net_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Take Home</h5>
                    <h2 class="card-text">£{{ lesson_stats.after_tax_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Week Statistics -->
    <div class="row mb-4">
        <h4 class="mb-3">Current Week (Mon-Sun)</h4>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Lessons</h5>
                    <h2 class="card-text">{{ current_week_stats.total_lessons }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Hours</h5>
                    <h2 class="card-text">{{ current_week_stats.total_hours|floatformat:1 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Income</h5>
                    <h2 class="card-text">£{{ current_week_stats.total_income|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Expenses</h5>
                    <h2 class="card-text">£{{ current_week_stats.total_expenses|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Gross</h5>
                    <h2 class="card-text">£{{ current_week_stats.net_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Take Home</h5>
                    <h2 class="card-text">£{{ current_week_stats.after_tax_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Last Full 7 Days Statistics -->
    <div class="row mb-4">
        <h4 class="mb-3">Last 7 Days Including Today</h4>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Lessons</h5>
                    <h2 class="card-text">{{ last_seven_days_stats.total_lessons }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Hours</h5>
                    <h2 class="card-text">{{ last_seven_days_stats.total_hours|floatformat:1 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Income</h5>
                    <h2 class="card-text">£{{ last_seven_days_stats.total_income|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Expenses</h5>
                    <h2 class="card-text">£{{ last_seven_days_stats.total_expenses|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Gross</h5>
                    <h2 class="card-text">£{{ last_seven_days_stats.net_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Take Home</h5>
                    <h2 class="card-text">£{{ last_seven_days_stats.after_tax_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Statistics -->
    <div class="row mb-4">
        <h4 class="mb-3">Last 30 Days Including Today</h4>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Lessons</h5>
                    <h2 class="card-text">{{ monthly_stats.total_lessons }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Hours</h5>
                    <h2 class="card-text">{{ monthly_stats.total_hours|floatformat:1 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Income</h5>
                    <h2 class="card-text">£{{ monthly_stats.total_income|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Expenses</h5>
                    <h2 class="card-text">£{{ monthly_stats.total_expenses|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Gross</h5>
                    <h2 class="card-text">£{{ monthly_stats.net_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Take Home</h5>
                    <h2 class="card-text">£{{ monthly_stats.after_tax_earnings|floatformat:2 }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Trends Section -->
    <div class="mb-4 font-bold">
        <h3>Trends</h3>
        <p class="text-muted">
            <i class="fas fa-info-circle"></i><br>Figures shown in <span class="text-danger">red</span> are not final as the respective period is still ongoing. 'Gross' = Earning after expense but before Tax & NI - and 'Net' = earning after expenses and after approximate income Tax and NI deductions as defined on the 'My Data' page.
        </p>
    </div>

    <!-- Weekly Trends -->
    <div class="card mb-4">
        <div class="card-header">
            <h4>Weekly Trends</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Week Period</th>
                            <th>Lessons</th>
                            <th>Hours</th>
                            <th>Income</th>
                            <th>Expenses</th>
                            <th>Gross/Net</th>
                            <th>Per Hour Gross/Net</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trend in weekly_trends %}
                        <tr {% if forloop.first %}class="text-danger fw-bold"{% endif %}>
                            <td>{{ trend.week|date:"d M" }} - {{ trend.week_end|date:"d M Y" }}</td>
                            <td>{{ trend.lesson_count }}</td>
                            <td>{{ trend.total_hours|floatformat:1 }}</td>
                            <td>£{{ trend.total_income|floatformat:2 }}</td>
                            <td>£{{ trend.total_expenses|floatformat:2 }}</td>
                            <td>£{{ trend.net_earnings|floatformat:2 }} / £{{ trend.after_tax_earnings|floatformat:2 }}</td>
                            <td>£{{ trend.earnings_per_hour|floatformat:2 }} / £{{ trend.after_tax_earnings_per_hour|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Monthly Trends -->
    <div class="card mb-4">
        <div class="card-header">
            <h4>Monthly Trends</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Month</th>
                            <th>Lessons</th>
                            <th>Hours</th>
                            <th>Income</th>
                            <th>Expenses</th>
                            <th>Gross/Net</th>
                            <th>Per Hour Gross/Net</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trend in monthly_trends %}
                        <tr {% if forloop.first %}class="text-danger fw-bold"{% endif %}>
                            <td>{{ trend.month|date:"F Y" }}</td>
                            <td>{{ trend.lesson_count }}</td>
                            <td>{{ trend.total_hours|floatformat:1 }}</td>
                            <td>£{{ trend.total_income|floatformat:2 }}</td>
                            <td>£{{ trend.total_expenses|floatformat:2 }}</td>
                            <td>£{{ trend.net_earnings|floatformat:2 }} / £{{ trend.after_tax_earnings|floatformat:2 }}</td>
                            <td>£{{ trend.earnings_per_hour|floatformat:2 }} / £{{ trend.after_tax_earnings_per_hour|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quarterly Trends -->
    <div class="card mb-4">
        <div class="card-header">
            <h4>Quarterly Trends</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Quarter</th>
                            <th>Lessons</th>
                            <th>Hours</th>
                            <th>Income</th>
                            <th>Expenses</th>
                            <th>Gross/Net</th>
                            <th>Per Hour Gross/Net</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for trend in quarterly_trends %}
                        <tr {% if forloop.first %}class="text-danger fw-bold"{% endif %}>
                            <td>Q{{ trend.quarter_number }} {{ trend.year }} ({{ trend.quarter_start|date:"d M" }} - {{ trend.quarter_end|date:"d M Y" }})</td>
                            <td>{{ trend.lesson_count }}</td>
                            <td>{{ trend.total_hours|floatformat:1 }}</td>
                            <td>£{{ trend.total_income|floatformat:2 }}</td>
                            <td>£{{ trend.total_expenses|floatformat:2 }}</td>
                            <td>£{{ trend.net_earnings|floatformat:2 }} / £{{ trend.after_tax_earnings|floatformat:2 }}</td>
                            <td>£{{ trend.earnings_per_hour|floatformat:2 }} / £{{ trend.after_tax_earnings_per_hour|floatformat:2 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<br>
<br>
<br>
{% endblock %}
