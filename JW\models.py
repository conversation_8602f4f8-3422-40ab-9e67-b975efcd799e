from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal, ROUND_HALF_UP
import calendar

# Create your models here.

class AppJwStudent(models.Model):
    id = models.BigAutoField(primary_key=True)
    student_name = models.CharField(max_length=30, unique=True)
    mobile_number = models.CharField(max_length=20, blank=True, null=True)
    email_address = models.CharField(max_length=100, blank=True, null=True)
    address_1st_line = models.CharField(max_length=100, blank=True, null=True)
    address_2nd_line = models.CharField(max_length=100, blank=True, null=True)
    area = models.CharField(max_length=50, blank=True, null=True)
    post_code = models.CharField(max_length=10, blank=True, null=True)
    gender = models.Char<PERSON>ield(max_length=6, blank=True, null=True)
    age = models.IntegerField(blank=True, null=True)
    active = models.Char<PERSON>ield(
        max_length=6,
        default='Yes',
        choices=[
            ('Yes', 'Yes'),
            ('No', 'No'),
            ('Passed', 'Passed')
        ]
    )
    notes = models.TextField(blank=True, null=True)
    test_past = models.DateField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'APP_JW_student'
        ordering = ['student_name']

    def __str__(self):
        return self.student_name

class AppJwLesson(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    lesson_hours = models.DecimalField(max_digits=4, decimal_places=1)
    student_name = models.CharField(max_length=30)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')
    price_per_hour = models.DecimalField(max_digits=5, decimal_places=2)
    amount = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True)
    student = models.ForeignKey(
        AppJwStudent,
        models.DO_NOTHING,
        db_column='student_id',
        related_name='lessons'
    )

    class Meta:
        managed = False
        db_table = 'APP_JW_lesson'
        ordering = ['-date']

    @property
    def calculated_amount(self):
        if self.lesson_hours and self.price_per_hour:
            return Decimal(str(self.lesson_hours)) * Decimal(str(self.price_per_hour))
        return Decimal('0.00')

    def save(self, *args, **kwargs):
        # Calculate amount
        if self.lesson_hours and self.price_per_hour:
            self.amount = Decimal(str(self.lesson_hours)) * Decimal(str(self.price_per_hour))
        super().save(*args, **kwargs)

class AppJwBusinessexpense(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    expense_type = models.CharField(max_length=20)
    cost = models.DecimalField(max_digits=6, decimal_places=2)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')
    category = models.CharField(max_length=20, blank=True, null=True)
    description = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'APP_JW_businessexpense'
        ordering = ['-date']

    def save(self, *args, **kwargs):
        # Set day of week
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

class AppJwMileage(models.Model):
    date = models.DateField()
    day = models.CharField(max_length=10, blank=True)
    mileage_type = models.CharField(max_length=10, choices=[('Business', 'Business'), ('Personal', 'Personal')])
    miles = models.DecimalField(max_digits=5, decimal_places=1)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'app_jw_mileage'
        managed = False

    def save(self, *args, **kwargs):
        if self.date and not self.day:
            self.day = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.date} - {self.miles} miles ({self.mileage_type})"

class AppJwBusinessmileage(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    mileage = models.DecimalField(max_digits=5, decimal_places=1)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')

    class Meta:
        managed = False
        db_table = 'APP_JW_businessmileage'
        ordering = ['-date']

    def save(self, *args, **kwargs):
        # Set day of week
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

class AppJwPersonalmileage(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    mileage = models.DecimalField(max_digits=5, decimal_places=1)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')

    class Meta:
        managed = False
        db_table = 'APP_JW_personalmileage'
        ordering = ['-date']

    def save(self, *args, **kwargs):
        # Set day of week
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

class AppJwFuelexpense(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    cost = models.DecimalField(max_digits=6, decimal_places=2)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')

    class Meta:
        managed = False
        db_table = 'APP_JW_fuelexpense'
        ordering = ['-date']

    def save(self, *args, **kwargs):
        # Set day of week
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

    def clean(self):
        # Ensure day_of_week is always set correctly
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().clean()

class AppJwFailedTest(models.Model):
    student_name = models.CharField(max_length=30)
    test_date = models.DateField()
    lesson = models.ForeignKey(AppJwLesson, models.DO_NOTHING, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'APP_JW_failed_test'

class AppJwReportHeader(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    field_name = models.CharField(max_length=50)  # e.g., 'name', 'address', etc.
    field_value = models.CharField(max_length=255)
    is_enabled = models.BooleanField(default=False)
    display_order = models.IntegerField(default=0)

    class Meta:
        db_table = 'APP_JW_report_header'
        unique_together = ['user', 'field_name']

class AppJwUserSettings(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='jw_settings')
    tax_ni_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=21.00)

    class Meta:
        db_table = 'APP_JW_user_settings'

class AppJwBlockBooking(models.Model):
    id = models.BigAutoField(primary_key=True)
    student = models.ForeignKey(AppJwStudent, models.DO_NOTHING, db_column='student_id', related_name='block_bookings')
    date_created = models.DateField()
    amount_paid = models.DecimalField(max_digits=6, decimal_places=2)
    total_lessons = models.DecimalField(max_digits=4, decimal_places=1)
    lessons_used = models.DecimalField(max_digits=4, decimal_places=1, default=0)
    active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, null=True)
    # Temporary field to force migration
    # temp_field = models.CharField(max_length=1, blank=True, null=True)

    # New fields for enhanced block booking system
    price_per_lesson_fixed = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    remainder_balance = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    calculation_method = models.CharField(max_length=10, default='legacy', choices=[
        ('legacy', 'Legacy (Amount ÷ Lessons)'),
        ('new', 'New (Amount ÷ Price + Remainder)')
    ])

    class Meta:
        managed = False
        db_table = 'APP_JW_block_booking'
        ordering = ['-date_created']

    @property
    def lessons_remaining(self):
        return self.total_lessons - self.lessons_used

    @property
    def price_per_lesson(self):
        """Get price per lesson based on calculation method, rounded to nearest 0.50 for legacy."""
        if self.calculation_method == 'new' and self.price_per_lesson_fixed is not None:
            return self.price_per_lesson_fixed
        elif self.total_lessons and self.total_lessons > 0:
            # Legacy calculation: amount_paid ÷ total_lessons
            raw_price = self.amount_paid / self.total_lessons
            # Round to nearest 0.50
            return (raw_price * 2).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / 2
        return Decimal('0.00')

    @property
    def is_fully_used(self):
        return self.lessons_used >= self.total_lessons

    @property
    def total_value_remaining(self):
        """Total value including lessons + remainder balance"""
        lessons_value = self.lessons_remaining * self.price_per_lesson
        return lessons_value + (self.remainder_balance or Decimal('0.00'))

    @property
    def effective_lessons_remaining(self):
        """Lessons remaining including remainder balance converted to lessons"""
        if self.price_per_lesson > 0:
            remainder_as_lessons = (self.remainder_balance or Decimal('0.00')) / self.price_per_lesson
            return self.lessons_remaining + remainder_as_lessons
        return self.lessons_remaining

    def save(self, *args, **kwargs):
        # Only calculate remainder_balance for legacy method if not explicitly set
        if self.calculation_method == 'legacy' and self.amount_paid is not None and self.total_lessons is not None:
            if self.total_lessons > 0:
                # Calculate price per lesson rounded to 2 decimal places
                calculated_price_per_lesson = (self.amount_paid / self.total_lessons).quantize(Decimal('0.01'))
                # Calculate the value covered by the whole/half lessons
                covered_value = self.total_lessons * calculated_price_per_lesson
                self.remainder_balance = self.amount_paid - covered_value
            else:
                self.remainder_balance = self.amount_paid # If no lessons, all is remainder

        # For 'new' calculation method, don't override remainder_balance unless it's None
        elif self.calculation_method == 'new' and self.remainder_balance is None:
            # Calculate remainder for new method: amount_paid - (total_lessons * price_per_lesson_fixed)
            if (self.amount_paid is not None and self.total_lessons is not None and
                self.price_per_lesson_fixed is not None):
                lesson_cost = self.total_lessons * self.price_per_lesson_fixed
                self.remainder_balance = self.amount_paid - lesson_cost
            else:
                self.remainder_balance = Decimal('0.00')

        # Ensure remainder_balance is set to 0 if None (but allow negative values for overpayments)
        if self.remainder_balance is None:
            self.remainder_balance = Decimal('0.00')

        super().save(*args, **kwargs)

    def add_usage(self, hours_to_add, price_per_hour_of_lesson):
        """
        Adds usage to the block booking, adjusting lessons_used and remainder_balance.
        Returns True if successful, False if not enough credit.
        """
        from decimal import Decimal
        
        # Ensure current state is loaded
        self.refresh_from_db()

        # Calculate the value of the current lesson
        value_to_add = Decimal(str(hours_to_add)) * Decimal(str(price_per_hour_of_lesson))

        # Check if there's enough total value remaining
        if value_to_add > self.total_value_remaining:
            return False # Not enough credit

        if self.calculation_method == 'new':
            # Use lesson allocation first, then remainder for any excess
            if self.price_per_lesson_fixed and self.price_per_lesson_fixed > 0:
                # Calculate how much lesson allocation is available
                lessons_available = self.total_lessons - self.lessons_used
                lesson_value_available = lessons_available * self.price_per_lesson_fixed

                if value_to_add <= lesson_value_available:
                    # Can be covered entirely by lesson allocation
                    hours_from_lessons = value_to_add / self.price_per_lesson_fixed
                    self.lessons_used += hours_from_lessons
                else:
                    # Use all remaining lesson allocation, then remainder for excess
                    self.lessons_used = self.total_lessons  # Use all lessons
                    excess_value = value_to_add - lesson_value_available

                    # Deduct excess from remainder
                    if self.remainder_balance >= excess_value:
                        self.remainder_balance -= excess_value
                    else:
                        # Not enough total credit - this should have been caught earlier
                        raise ValueError("Insufficient credit for this lesson")
            else:
                # Fallback or error if price_per_lesson_fixed is not set for 'new' method
                raise ValueError("price_per_lesson_fixed must be set for 'new' calculation method.")
        else: # legacy method
            # Directly deduct hours from total_lessons
            self.lessons_used += Decimal(str(hours_to_add))

        # Ensure lessons_used does not exceed total_lessons (can happen with floating point arithmetic or edge cases)
        if self.lessons_used > self.total_lessons:
            self.lessons_used = self.total_lessons

        # Deactivate if fully used (lessons_used >= total_lessons and remainder_balance is zero or negligible)
        if self.lessons_used >= self.total_lessons and self.remainder_balance <= Decimal('0.01'): # Allow for tiny floating point remainders
            self.active = False
        
        self.save()
        return True

    def remove_usage(self, hours_to_remove, price_per_hour_of_lesson):
        """
        Removes usage from the block booking, returning credit.
        """
        from decimal import Decimal
        self.refresh_from_db()

        if self.calculation_method == 'new':
            value_to_return = Decimal(str(hours_to_remove)) * Decimal(str(price_per_hour_of_lesson))

            # Return credit by reversing the add_usage logic:
            # First try to return to lesson allocation, then to remainder
            if self.lessons_used >= self.total_lessons:
                # All lessons were used, so this removal should go to remainder
                self.remainder_balance += value_to_return
                # Also free up some lesson allocation if possible
                hours_to_free = min(Decimal(str(hours_to_remove)), self.lessons_used)
                self.lessons_used -= hours_to_free
            else:
                # Some lesson allocation is still available, return to lesson allocation
                hours_to_return = value_to_return / self.price_per_lesson_fixed
                self.lessons_used -= hours_to_return
                if self.lessons_used < 0:
                    # Overage goes to remainder
                    overage_hours = abs(self.lessons_used)
                    overage_value = overage_hours * self.price_per_lesson_fixed
                    self.remainder_balance += overage_value
                    self.lessons_used = Decimal('0.00')
        else: # legacy method
            self.lessons_used -= Decimal(str(hours_to_remove))
            if self.lessons_used < 0:
                self.lessons_used = Decimal('0.00') # Prevent negative usage

        # Reactivate the block booking if it was previously deactivated
        self.active = True
        self.save()

class AppJwBlockBookingUsage(models.Model):
    id = models.BigAutoField(primary_key=True)
    block_booking = models.ForeignKey(AppJwBlockBooking, models.DO_NOTHING, related_name='usages')
    lesson = models.ForeignKey(AppJwLesson, models.DO_NOTHING, related_name='block_booking_usage')
    lessons_used = models.DecimalField(max_digits=4, decimal_places=1)
    date_used = models.DateField()

    class Meta:
        managed = False
        db_table = 'APP_JW_block_booking_usage'
        ordering = ['-date_used']
