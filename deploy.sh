#!/bin/bash

# LTDWJ Docker Deployment Script
# Run this script on your Debian 12 server

echo "🚀 Starting LTDWJ Docker Deployment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Installing Docker..."

    # Update package index
    sudo apt-get update

    # Install prerequisites
    sudo apt-get install -y \
        ca-certificates \
        curl \
        gnupg \
        lsb-release

    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

    # Set up the repository
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian \
      $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

    # Update package index again
    sudo apt-get update

    # Install Docker Engine
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

    # Add current user to docker group
    sudo usermod -aG docker $USER

    echo "✅ Docker installed successfully!"
    echo "⚠️  Please log out and log back in for group changes to take effect."
    echo "⚠️  Then run this script again."
    exit 0
fi

# Check if Docker Compose is available
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

echo "✅ Docker is installed and ready!"

# Stop and remove existing container if it exists
echo "🛑 Stopping existing container (if any)..."
docker stop LTDWJ-25052025 2>/dev/null || true
docker rm LTDWJ-25052025 2>/dev/null || true

# Create directories for static and media files
echo "📁 Creating static and media directories..."
mkdir -p ./staticfiles ./media

# Build and run the container
echo "🔨 Building Docker image..."
docker build -t ltdwj-app .

echo "🚀 Starting container..."
docker run -d \
    --name LTDWJ-25052025 \
    --restart always \
    -p **********:8000:8000 \
    -v $(pwd)/staticfiles:/app/staticfiles \
    -v $(pwd)/media:/app/media \
    ltdwj-app

# Check if container is running
if docker ps | grep -q LTDWJ-25052025; then
    echo "✅ Container LTDWJ-25052025 is running successfully!"
    echo "🌐 Application is available at: http://**********:8000"
    echo "📊 Container status:"
    docker ps | grep LTDWJ-25052025
else
    echo "❌ Container failed to start. Checking logs..."
    docker logs LTDWJ-25052025
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Useful commands:"
echo "  View logs:     docker logs LTDWJ-25052025"
echo "  Stop:          docker stop LTDWJ-25052025"
echo "  Start:         docker start LTDWJ-25052025"
echo "  Restart:       docker restart LTDWJ-25052025"
echo "  Remove:        docker stop LTDWJ-25052025 && docker rm LTDWJ-25052025"
echo ""
echo "📊 Database: External PostgreSQL at 10.10.10.2:5432"
echo "🗄️  Database Name: JW"
echo "👤 Database User: postgres"
