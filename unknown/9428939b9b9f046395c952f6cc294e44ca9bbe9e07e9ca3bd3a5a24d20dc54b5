{% extends 'JW/base.html' %}
{% load student_filters %}

{% block title %}Student List{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <!-- Page Header and Stats - Only visible on xl screens -->
    <h2 class="d-none d-xl-block mb-4">Students</h2>
    
    <!-- Stats Cards Row -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">   
        <a href="?status=all" class="text-decoration-none flex-grow-1">
            <div class="card h-100 {% if current_filter == 'all' or not current_filter %}border-primary{% endif %}">
                <div class="card-body text-center">
                    <h5 class="card-title {% if current_filter == 'all' or not current_filter %}text-primary{% else %}text-dark{% endif %}">Total Students</h5>
                    <h2 class="mb-0 {% if current_filter == 'all' or not current_filter %}text-primary{% else %}text-dark{% endif %}">{{ total_students }}</h2>
                </div>
            </div>
        </a>

        <a href="?status=active" class="text-decoration-none flex-grow-1">
            <div class="card h-100 {% if current_filter == 'active' %}border-primary{% endif %}">
                <div class="card-body text-center">
                    <h5 class="card-title {% if current_filter == 'active' %}text-primary{% else %}text-dark{% endif %}">Active Students</h5>
                    <h2 class="mb-0 {% if current_filter == 'active' %}text-primary{% else %}text-dark{% endif %}">{{ active_students }}</h2>
                </div>
            </div>
        </a>

        <a href="?status=inactive" class="text-decoration-none flex-grow-1">
            <div class="card h-100 {% if current_filter == 'inactive' %}border-primary{% endif %}">
                <div class="card-body text-center">
                    <h5 class="card-title {% if current_filter == 'inactive' %}text-primary{% else %}text-dark{% endif %}">Inactive Students</h5>
                    <h2 class="mb-0 {% if current_filter == 'inactive' %}text-primary{% else %}text-dark{% endif %}">{{ inactive_students }}</h2>
                </div>
            </div>
        </a>

        <a href="?status=passed" class="text-decoration-none flex-grow-1">
            <div class="card h-100 {% if current_filter == 'passed' %}border-primary{% endif %}">
                <div class="card-body text-center">
                    <h5 class="card-title {% if current_filter == 'passed' %}text-primary{% else %}text-dark{% endif %}">Passed Students</h5>
                    <h2 class="mb-0 {% if current_filter == 'passed' %}text-primary{% else %}text-dark{% endif %}">{{ passed_students }}</h2>
                </div>
            </div>
        </a>

        <div class="card h-100 flex-grow-1">
            <div class="card-body text-center">
                <h5 class="card-title text-dark">Avg til Passed</h5>
                <h6 class="mb-0">{{ avg_lessons_to_pass }} lessons</h6>
                <h6 class="mb-0">{{ avg_hours_to_pass }} hours</h6>
            </div>
        </div>
    </div>

    <!-- Demographics Statistics -->
    <div class="d-none d-xl-block mb-4">
        <div class="row">
            <div class="col-xl-4">
                <!-- Gender Distribution Card -->
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">Gender Distribution</h6>
                            {% if current_gender %}
                            <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}" 
                               class="btn btn-sm btn-danger text-white">Clear</a>
                            {% endif %}
                        </div>
                        <div class="d-flex flex-wrap gap-2">
                            {% for gender in 'Male,Female,Other'|split:',' %}
                                <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}gender={{ gender }}" 
                                   class="text-decoration-none">
                                    <span class="badge {% if gender == current_gender %}bg-primary{% else %}bg-secondary{% endif %} clickable-badge">
                                        {{ gender }} ({{ students|filter_by_gender:gender|length }})
                                    </span>
                                </a>
                            {% endfor %}
                            <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}gender=blank" 
                               class="text-decoration-none">
                                <span class="badge {% if 'blank' == current_gender %}bg-primary{% else %}bg-warning text-dark{% endif %} clickable-badge">
                                    Blank ({{ students|filter_by_blank_gender|length }})
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4">
                <!-- Age Distribution Card -->
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">Age Distribution</h6>
                            {% if current_age_range %}
                            <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}" 
                               class="btn btn-sm btn-danger text-white">Clear</a>
                            {% endif %}
                        </div>
                        <div class="d-flex flex-wrap gap-2">
                            {% for range in '17-25,26-35,36-45,46-55,56+'|split:',' %}
                                <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}age_range={{ range }}" 
                                   class="text-decoration-none">
                                    <span class="badge {% if range == current_age_range %}bg-primary{% else %}bg-secondary{% endif %} clickable-badge">
                                        {{ range }} ({{ students|filter_by_age:range|length }})
                                    </span>
                                </a>
                            {% endfor %}
                            <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}age_range=blank" 
                               class="text-decoration-none">
                                <span class="badge {% if 'blank' == current_age_range %}bg-primary{% else %}bg-warning text-dark{% endif %} clickable-badge">
                                    Blank ({{ students|filter_by_blank_age|length }})
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4">
                <!-- Area Distribution Card -->
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">Area Distribution</h6>
                            {% if current_area %}
                            <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}" 
                               class="btn btn-sm btn-danger text-white">Clear</a>
                            {% endif %}
                        </div>
                        <div class="d-flex flex-wrap gap-2">
                            {% for area in areas %}
                                <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}area={{ area }}" 
                                   class="text-decoration-none">
                                    <span class="badge {% if area == current_area %}bg-primary{% else %}bg-secondary{% endif %} clickable-badge">
                                        {{ area }} ({{ students|filter_by_area:area|length }})
                                    </span>
                                </a>
                            {% endfor %}
                            <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}area=blank" 
                               class="text-decoration-none">
                                <span class="badge {% if 'blank' == current_area %}bg-primary{% else %}bg-warning text-dark{% endif %} clickable-badge">
                                    Blank ({{ students|filter_by_blank_area|length }})
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    .clickable-badge:hover {
        opacity: 0.8;
        cursor: pointer;
    }
    .blank-badge {
        background-color: #ffd700 !important; /* Yellow background */
        color: #ff0000 !important; /* Bright red text */
        font-weight: bold;
    }

    /* Sortable table headers */
    th a {
        display: block;
        padding: 8px 4px;
        color: inherit !important;
        text-decoration: none !important;
    }

    th a:hover {
        background-color: rgba(0,0,0,0.05);
        border-radius: 4px;
    }

    th a i.fa-sort {
        opacity: 0.3;
    }

    th a i.fa-sort-up,
    th a i.fa-sort-down {
        opacity: 1;
        color: #007bff;
    }
    </style>

    <!-- Add New Student button -->
    <div class="mb-3">
        <a href="{% url 'student_create' %}" class="btn btn-primary w-100 h-100 d-flex align-items-center justify-content-center py-2">
            <div class="text-center">
                <i class="fas fa-user-plus fa-2x mb-2"></i>
                <div>New Student</div>
            </div>
        </a>
    </div>

    <!-- Student filter input -->
    <div class="row mb-3">
        <div class="col">
            <input type="text" id="studentFilter" class="form-control" placeholder="Filter students...">
        </div>
        {% if current_filter or current_gender or current_age_range or current_area %}
        <div class="col-auto">
            <a href="?" class="btn" style="background-color: #00ff00; color: black; font-weight: bold;">Clear All Filters</a>
        </div>
        {% endif %}
    </div>

    <table class="table table-striped">
        <thead>
            <tr>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=name&order={% if current_sort == 'name' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        Name
                        {% if current_sort == 'name' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=lessons&order={% if current_sort == 'lessons' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        All Lessons
                        {% if current_sort == 'lessons' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=hours&order={% if current_sort == 'hours' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        Hours
                        {% if current_sort == 'hours' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=revenue&order={% if current_sort == 'revenue' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        Revenue
                        {% if current_sort == 'revenue' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=block_booking&order={% if current_sort == 'block_booking' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        Block-B
                        {% if current_sort == 'block_booking' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=last_lesson&order={% if current_sort == 'last_lesson' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        Last Lesson
                        {% if current_sort == 'last_lesson' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>
                    <a href="?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}sort=status&order={% if current_sort == 'status' and current_order == 'asc' %}desc{% else %}asc{% endif %}"
                       class="text-decoration-none text-dark">
                        Status
                        {% if current_sort == 'status' %}
                            {% if current_order == 'asc' %}
                                <i class="fas fa-sort-up"></i>
                            {% else %}
                                <i class="fas fa-sort-down"></i>
                            {% endif %}
                        {% else %}
                            <i class="fas fa-sort text-muted"></i>
                        {% endif %}
                    </a>
                </th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for student in students %}
            <tr>
                <td>{{ student.student_name }}</td>
                <td>{{ student.lesson_count }}</td>
                <td>{{ student.total_hours|default:"0" }}</td>
                <td>£{{ student.total_amount|default:"0" }}</td>
                <td>
                    {% if student.has_block_booking %}
                        <span class="badge bg-success">Y</span>
                    {% else %}
                        <span class="badge bg-secondary">N</span>
                    {% endif %}
                </td>
                <td>{{ student.last_lesson_date|date:"d/m/Y"|default:"Never" }}</td>
                <td>{{ student.active }}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{% url 'student_detail' student.id %}" class="btn btn-sm btn-info">View</a>
                        <a href="{% url 'student_edit' student.id %}?{% if current_filter %}status={{ current_filter }}&{% endif %}{% if current_gender %}gender={{ current_gender }}&{% endif %}{% if current_age_range %}age_range={{ current_age_range }}&{% endif %}{% if current_area %}area={{ current_area }}&{% endif %}" 
                           class="btn btn-sm btn-warning">Edit</a>
                        <a href="{% url 'student_delete' student.id %}" class="btn btn-sm btn-danger">Delete</a>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock content %}

{% block javascript %}
<script>
document.getElementById('studentFilter').addEventListener('input', function() {
    const filterValue = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('table tbody tr');
    
    tableRows.forEach(row => {
        const studentName = row.querySelector('td:first-child').textContent.toLowerCase();
        row.style.display = studentName.includes(filterValue) ? '' : 'none';
    });
});

// Add hover effect to cards
document.querySelectorAll('.card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        if (!this.classList.contains('border-primary')) {
            this.classList.add('shadow-sm');
        }
    });
    
    card.addEventListener('mouseleave', function() {
        if (!this.classList.contains('border-primary')) {
            this.classList.remove('shadow-sm');
        }
    });
});
</script>
{% endblock javascript %}
