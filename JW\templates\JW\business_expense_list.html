{% extends 'JW/base.html' %}

{% block title %}Business Expenses{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <!-- Page Header and Stats - Only visible on xl screens -->
    <h2 class="d-none d-xl-block mb-4">Business Expenses</h2>
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Entries</div>
                    <div class="h4">{{ expenses|length }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Total Cost</div>
                    <div class="h4">£{{ total_cost|floatformat:2 }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Average Cost</div>
                    <div class="h4">£{{ avg_cost|floatformat:2 }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Expense Types</div>
                    <div class="h4">{{ distinct_expense_types }}</div>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Top Type</div>
                    <div class="h4">{{ top_expense_type|default:'N/A' }}</div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-4 mb-3">
        <a href="{% url 'business_expense_create' %}" class="btn btn-primary w-100">Add New Expense</a>
    </div>
    <div class="mb-3">
        <input type="text" id="expenseFilter" class="form-control w-100" placeholder="Filter by expense type...">
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Day</th>
                    <th>Type</th>
                    <th>Cost</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for expense in expenses %}
                <tr>
                    <td>{{ expense.date|date:"M d, Y" }}</td>
                    <td>{{ expense.day_of_week }}</td>
                    <td>
                        {% if expense.expense_type %}
                            <a href="{% url 'expense_type_details' expense_type=expense.expense_type %}" class="text-decoration-none">
                                {{ expense.expense_type }}
                            </a>
                        {% else %}
                            <span class="text-muted">No type specified</span>
                        {% endif %}
                    </td>
                    <td>£{{ expense.cost|floatformat:2 }}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'business_expense_update' expense.pk %}" class="btn btn-sm btn-warning">Edit</a>
                            <a href="{% url 'business_expense_delete' expense.pk %}" class="btn btn-sm btn-danger">Delete</a>
                            {% if expense.description or expense.notes %}
                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#notesModal{{ expense.pk }}">
                                    View Details
                                </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>

                {% if expense.description or expense.notes %}
                <div class="modal fade" id="notesModal{{ expense.pk }}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Details for {{ expense.date|date:"M d, Y" }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                {% if expense.description %}
                                <h6>Description:</h6>
                                <p>{{ expense.description }}</p>
                                {% endif %}
                                {% if expense.notes %}
                                <h6>Notes:</h6>
                                <p>{{ expense.notes }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% empty %}
                <tr>
                    <td colspan="5" class="text-center">No business expenses found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block javascript %}
{{ block.super }}
<script>
document.getElementById('expenseFilter').addEventListener('input', function() {
    const filterValue = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('table tbody tr');
    
    tableRows.forEach(row => {
        const expenseType = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        row.style.display = expenseType.includes(filterValue) ? '' : 'none';
    });
});
</script>
{% endblock %}
