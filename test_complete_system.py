#!/usr/bin/env python
"""
Complete test of the enhanced block booking system
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking
from decimal import Decimal
from datetime import date

def test_complete_system():
    """Test the complete enhanced block booking system"""
    print("Testing Complete Enhanced Block Booking System")
    print("=" * 60)
    
    try:
        mark_smith = AppJwStudent.objects.get(student_name="<PERSON>")
        print(f"Testing with student: {mark_smith.student_name}")
        
        # Get current booking
        booking = AppJwBlockBooking.objects.filter(student=mark_smith, active=True).first()
        
        print(f"\n1. Current Block Booking State:")
        print(f"   Amount paid: £{booking.amount_paid}")
        print(f"   Total lessons: {booking.total_lessons}")
        print(f"   Price per lesson: £{booking.price_per_lesson_fixed}")
        print(f"   Lessons used: {booking.lessons_used}")
        print(f"   Remainder balance: £{booking.remainder_balance}")
        print(f"   Total value remaining: £{booking.total_value_remaining}")
        
        # Test the calculation logic
        print(f"\n2. Calculation Verification:")
        expected_lesson_cost = booking.total_lessons * booking.price_per_lesson_fixed
        expected_initial_remainder = booking.amount_paid - expected_lesson_cost
        print(f"   Expected lesson cost: {booking.total_lessons} × £{booking.price_per_lesson_fixed} = £{expected_lesson_cost}")
        print(f"   Expected initial remainder: £{booking.amount_paid} - £{expected_lesson_cost} = £{expected_initial_remainder}")
        
        lessons_value_remaining = (booking.total_lessons - booking.lessons_used) * booking.price_per_lesson_fixed
        expected_total_remaining = lessons_value_remaining + booking.remainder_balance
        print(f"   Lessons value remaining: {booking.total_lessons - booking.lessons_used} × £{booking.price_per_lesson_fixed} = £{lessons_value_remaining}")
        print(f"   Expected total remaining: £{lessons_value_remaining} + £{booking.remainder_balance} = £{expected_total_remaining}")
        print(f"   Actual total remaining: £{booking.total_value_remaining}")
        print(f"   ✅ Calculation correct: {abs(float(expected_total_remaining) - float(booking.total_value_remaining)) < 0.01}")
        
        # Test lesson recording
        print(f"\n3. Testing Lesson Recording:")
        print(f"   Before recording lesson:")
        print(f"     Total value remaining: £{booking.total_value_remaining}")
        print(f"     Remainder balance: £{booking.remainder_balance}")
        print(f"     Lessons used: {booking.lessons_used}")
        
        # Try to record a £20 lesson (should succeed)
        success = booking.add_usage(1.0, 20.00)
        print(f"   Recording 1 hour lesson at £20/hour: Success = {success}")
        
        if success:
            print(f"   After recording lesson:")
            print(f"     Total value remaining: £{booking.total_value_remaining}")
            print(f"     Remainder balance: £{booking.remainder_balance}")
            print(f"     Lessons used: {booking.lessons_used}")
            
            # Try to record another £20 lesson (should fail - not enough credit)
            success2 = booking.add_usage(1.0, 20.00)
            print(f"   Recording another 1 hour lesson at £20/hour: Success = {success2}")
            if not success2:
                print(f"   ✅ Correctly prevented overspending")
                print(f"   Available credit: £{booking.total_value_remaining}")
        
        # Test what happens with a new block booking
        print(f"\n4. Testing New Block Booking Creation:")
        print(f"   Current remainder that should carry over: £{booking.remainder_balance}")
        
        # Simulate creating a new block booking
        print(f"   If student pays £60 for 2 lessons at £25 each:")
        new_amount = Decimal('60.00')
        new_lessons = Decimal('2.0')
        new_price = Decimal('25.00')
        previous_remainder = booking.remainder_balance
        
        lesson_cost = new_lessons * new_price
        total_available = new_amount + previous_remainder
        new_remainder = total_available - lesson_cost
        
        print(f"     Lesson cost: {new_lessons} × £{new_price} = £{lesson_cost}")
        print(f"     Total available: £{new_amount} + £{previous_remainder} = £{total_available}")
        print(f"     New remainder: £{total_available} - £{lesson_cost} = £{new_remainder}")
        
        print(f"\n5. Summary:")
        print(f"   ✅ Block booking shows correct remainder: £{booking.remainder_balance}")
        print(f"   ✅ Total value calculation is correct: £{booking.total_value_remaining}")
        print(f"   ✅ Lesson recording works with remainder logic")
        print(f"   ✅ System prevents overspending")
        print(f"   ✅ New block booking would correctly carry over remainder")
        
    except AppJwStudent.DoesNotExist:
        print("Mark Smith not found in database")
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("Test completed!")

def test_specific_issue():
    """Test the specific issue: £100 for 2 lessons at £35 each, recording 2 hours at £35/hour"""
    print("Testing Specific Issue: Remainder Not Preserved")
    print("=" * 60)

    try:
        # Create or get test student
        student, created = AppJwStudent.objects.get_or_create(
            student_name='Test Student Issue',
            defaults={
                'mobile_number': '1234567890',
                'email_address': '<EMAIL>',
                'gender': 'Male',
                'age': 25,
                'address_1st_line': '123 Test St',
                'area': 'Test Area',
                'post_code': 'TE1 1ST',
                'active': 'Yes'
            }
        )

        # Delete any existing bookings for clean test
        AppJwBlockBooking.objects.filter(student=student).delete()

        # Create the problematic booking: £100 for 2 lessons at £35 each
        booking = AppJwBlockBooking.objects.create(
            student=student,
            date_created=date.today(),
            amount_paid=Decimal('100.00'),
            total_lessons=Decimal('2.0'),
            price_per_lesson_fixed=Decimal('35.00'),
            calculation_method='new',
            active=True,
            lessons_used=Decimal('0.0'),
            remainder_balance=Decimal('30.00')  # £100 - (2 × £35) = £30
        )

        print(f"1. Initial Booking State:")
        print(f"   Amount paid: £{booking.amount_paid}")
        print(f"   Total lessons: {booking.total_lessons}")
        print(f"   Price per lesson: £{booking.price_per_lesson_fixed}")
        print(f"   Lessons used: {booking.lessons_used}")
        print(f"   Remainder balance: £{booking.remainder_balance}")
        print(f"   Total value remaining: £{booking.total_value_remaining}")

        # Test recording 2 hours at £35/hour = £70 total
        print(f"\n2. Recording 2 hours at £35/hour (£70 total):")
        print(f"   This should use: 2 lessons (£70) from lesson allocation")
        print(f"   Remainder should stay: £30")

        success = booking.add_usage(2.0, 35.00)
        print(f"   Recording successful: {success}")

        if success:
            print(f"\n3. After Recording:")
            print(f"   Lessons used: {booking.lessons_used}")
            print(f"   Remainder balance: £{booking.remainder_balance}")
            print(f"   Total value remaining: £{booking.total_value_remaining}")

            # Verify the result
            expected_lessons_used = Decimal('2.0')  # All lessons used
            expected_remainder = Decimal('30.00')   # Should remain unchanged
            expected_total_remaining = Decimal('30.00')  # Only remainder left

            print(f"\n4. Verification:")
            print(f"   Expected lessons used: {expected_lessons_used}, Actual: {booking.lessons_used}")
            print(f"   Expected remainder: £{expected_remainder}, Actual: £{booking.remainder_balance}")
            print(f"   Expected total remaining: £{expected_total_remaining}, Actual: £{booking.total_value_remaining}")

            lessons_correct = abs(booking.lessons_used - expected_lessons_used) < Decimal('0.01')
            remainder_correct = abs(booking.remainder_balance - expected_remainder) < Decimal('0.01')
            total_correct = abs(booking.total_value_remaining - expected_total_remaining) < Decimal('0.01')

            print(f"   ✅ Lessons used correct: {lessons_correct}")
            print(f"   ✅ Remainder preserved: {remainder_correct}")
            print(f"   ✅ Total remaining correct: {total_correct}")

            if lessons_correct and remainder_correct and total_correct:
                print(f"\n🎉 SUCCESS: Issue has been fixed!")
            else:
                print(f"\n❌ FAILURE: Issue still exists")

        # Clean up
        booking.delete()
        if created:
            student.delete()

    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_specific_issue()
    print()
    test_complete_system()
