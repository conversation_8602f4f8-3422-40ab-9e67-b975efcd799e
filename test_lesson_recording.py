#!/usr/bin/env python
"""
Test lesson recording with the enhanced block booking system
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking
from decimal import Decimal

def test_lesson_recording():
    """Test lesson recording with block booking credit"""
    print("Testing Lesson Recording with Block Bookings")
    print("=" * 50)
    
    try:
        mark_smith = AppJwStudent.objects.get(student_name="<PERSON>")
        print(f"Testing with student: {mark_smith.student_name}")
        
        # Get current active bookings
        bookings = AppJwBlockBooking.objects.filter(student=mark_smith, active=True).order_by('-date_created')
        print(f"\nCurrent active bookings: {bookings.count()}")
        
        for i, booking in enumerate(bookings, 1):
            print(f"Booking {i}: £{booking.total_value_remaining:.2f} remaining")
        
        # Test booking selection logic (should select the one with most credit)
        print(f"\nTesting booking selection logic:")
        potential_bookings = AppJwBlockBooking.objects.filter(
            student=mark_smith,
            active=True
        ).order_by('-date_created')
        
        selected_booking = None
        max_credit = 0
        for booking in potential_bookings:
            print(f"  Checking booking {booking.id}: £{booking.total_value_remaining:.2f} remaining")
            if booking.total_value_remaining > max_credit:
                max_credit = booking.total_value_remaining
                selected_booking = booking
                print(f"    -> New best booking!")
        
        if selected_booking:
            print(f"\nSelected booking {selected_booking.id} with £{selected_booking.total_value_remaining:.2f} remaining")
            
            # Test adding a lesson
            print(f"\nTesting lesson recording:")
            print(f"Before lesson - Booking {selected_booking.id}:")
            print(f"  Lessons used: {selected_booking.lessons_used}")
            print(f"  Remainder: £{selected_booking.remainder_balance}")
            print(f"  Total remaining: £{selected_booking.total_value_remaining}")
            
            # Simulate recording a 1-hour lesson at £35/hour
            success = selected_booking.add_usage(1.0, 35.00)
            print(f"\nRecorded 1 hour lesson at £35/hour: Success = {success}")
            
            print(f"After lesson - Booking {selected_booking.id}:")
            print(f"  Lessons used: {selected_booking.lessons_used}")
            print(f"  Remainder: £{selected_booking.remainder_balance}")
            print(f"  Total remaining: £{selected_booking.total_value_remaining}")
            
            # Test what happens if we try to record another expensive lesson
            print(f"\nTesting recording another £35 lesson:")
            success2 = selected_booking.add_usage(1.0, 35.00)
            print(f"Success = {success2}")
            if success2:
                print(f"After second lesson:")
                print(f"  Lessons used: {selected_booking.lessons_used}")
                print(f"  Remainder: £{selected_booking.remainder_balance}")
                print(f"  Total remaining: £{selected_booking.total_value_remaining}")
            else:
                print(f"Not enough credit for second £35 lesson")
                print(f"Available credit: £{selected_booking.total_value_remaining:.2f}")
        
    except AppJwStudent.DoesNotExist:
        print("Mark Smith not found in database")
    except Exception as e:
        print(f"Error in test: {e}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_lesson_recording()
