#!/usr/bin/env python
"""
Test the new block booking features:
1. Refund remainder functionality
2. Edit block booking with validation
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking, AppJwLesson
from decimal import Decimal
from datetime import date, timedelta

def test_refund_remainder():
    """Test the refund remainder functionality"""
    print("Testing Refund Remainder Functionality")
    print("=" * 50)
    
    try:
        # Get the test booking
        booking = AppJwBlockBooking.objects.get(id=34)
        student = booking.student
        
        print(f"Student: {student.student_name}")
        print(f"Before refund:")
        print(f"  Remainder balance: £{booking.remainder_balance}")
        print(f"  Total value remaining: £{booking.total_value_remaining}")
        
        # Simulate refund
        original_remainder = booking.remainder_balance
        booking.remainder_balance = Decimal('0.00')
        booking.save()
        
        print(f"\nAfter refund:")
        print(f"  Remainder balance: £{booking.remainder_balance}")
        print(f"  Total value remaining: £{booking.total_value_remaining}")
        print(f"  Refunded amount: £{original_remainder}")
        
        print("✅ Refund remainder test passed!")
        
        # Restore for other tests
        booking.remainder_balance = original_remainder
        booking.save()
        
    except Exception as e:
        print(f"❌ Refund remainder test failed: {e}")

def test_edit_block_booking_validation():
    """Test the edit block booking validation logic"""
    print("\nTesting Edit Block Booking Validation")
    print("=" * 50)
    
    try:
        # Get the test booking
        booking = AppJwBlockBooking.objects.get(id=34)
        student = booking.student
        
        print(f"Student: {student.student_name}")
        print(f"Current booking:")
        print(f"  Amount paid: £{booking.amount_paid}")
        print(f"  Total lessons: {booking.total_lessons}")
        print(f"  Price per lesson: £{booking.price_per_lesson_fixed}")
        print(f"  Lessons used: {booking.lessons_used}")
        print(f"  Remainder: £{booking.remainder_balance}")
        
        # Create some test lessons for this booking
        lesson1 = AppJwLesson.objects.create(
            student=student,
            student_name=student.student_name,
            date=booking.date_created + timedelta(days=1),
            lesson_hours=Decimal('1.0'),
            price_per_hour=Decimal('40.00'),
            amount=Decimal('40.00'),
            notes="Test lesson 1",
            day_of_week="Tuesday",
            user_id=1  # Assuming user ID 1 exists
        )
        
        lesson2 = AppJwLesson.objects.create(
            student=student,
            student_name=student.student_name,
            date=booking.date_created + timedelta(days=3),
            lesson_hours=Decimal('0.5'),
            price_per_hour=Decimal('40.00'),
            amount=Decimal('20.00'),
            notes="Test lesson 2",
            day_of_week="Thursday",
            user_id=1
        )
        
        print(f"\nCreated test lessons:")
        print(f"  Lesson 1: {lesson1.date}, {lesson1.lesson_hours}h, £{lesson1.amount}")
        print(f"  Lesson 2: {lesson2.date}, {lesson2.lesson_hours}h, £{lesson2.amount}")
        print(f"  Total lesson value: £{lesson1.amount + lesson2.amount}")
        
        # Test Case 1: Valid change (increase amount paid)
        print(f"\nTest Case 1: Increase amount paid to £200")
        new_amount = Decimal('200.00')
        new_lesson_allocation = booking.total_lessons * booking.price_per_lesson_fixed
        new_remainder = new_amount - new_lesson_allocation
        total_lesson_value = lesson1.amount + lesson2.amount
        
        print(f"  New lesson allocation: £{new_lesson_allocation}")
        print(f"  New remainder: £{new_remainder}")
        print(f"  Total available: £{new_lesson_allocation + new_remainder}")
        print(f"  Lessons used value: £{total_lesson_value}")
        
        if total_lesson_value <= (new_lesson_allocation + new_remainder):
            print("  ✅ This change would be VALID")
        else:
            print("  ❌ This change would be INVALID")
        
        # Test Case 2: Invalid change (reduce amount paid too much)
        print(f"\nTest Case 2: Reduce amount paid to £50")
        new_amount = Decimal('50.00')
        new_lesson_allocation = booking.total_lessons * booking.price_per_lesson_fixed
        new_remainder = new_amount - new_lesson_allocation
        
        print(f"  New lesson allocation: £{new_lesson_allocation}")
        print(f"  New remainder: £{new_remainder}")
        print(f"  Total available: £{new_lesson_allocation + new_remainder}")
        print(f"  Lessons used value: £{total_lesson_value}")
        
        if total_lesson_value <= (new_lesson_allocation + new_remainder):
            print("  ✅ This change would be VALID")
        else:
            print("  ❌ This change would be INVALID (as expected)")
        
        # Test Case 3: Change price per lesson
        print(f"\nTest Case 3: Change price per lesson to £50")
        new_price = Decimal('50.00')
        new_lesson_allocation = booking.total_lessons * new_price
        new_remainder = booking.amount_paid - new_lesson_allocation
        
        print(f"  New lesson allocation: £{new_lesson_allocation}")
        print(f"  New remainder: £{new_remainder}")
        print(f"  Total available: £{new_lesson_allocation + new_remainder}")
        print(f"  Lessons used value: £{total_lesson_value}")
        
        if total_lesson_value <= (new_lesson_allocation + new_remainder):
            print("  ✅ This change would be VALID")
        else:
            print("  ❌ This change would be INVALID")
        
        print("\n✅ Edit block booking validation test completed!")
        
        # Clean up test lessons
        lesson1.delete()
        lesson2.delete()
        print("✅ Test lessons cleaned up")
        
    except Exception as e:
        print(f"❌ Edit block booking validation test failed: {e}")
        import traceback
        traceback.print_exc()

def test_ui_integration():
    """Test that the UI components are properly integrated"""
    print("\nTesting UI Integration")
    print("=" * 50)
    
    try:
        # Check that the booking exists and has the right data for UI
        booking = AppJwBlockBooking.objects.get(id=34)
        
        print(f"Booking ID: {booking.id}")
        print(f"Student: {booking.student.student_name}")
        print(f"Remainder: £{booking.remainder_balance}")
        print(f"Active: {booking.active}")
        
        # Check if remainder > 0 (should show refund button)
        if booking.remainder_balance > 0:
            print("✅ Refund button should be visible (remainder > 0)")
        else:
            print("ℹ️ Refund button should be hidden (no remainder)")
        
        # Edit button should always be visible
        print("✅ Edit button should always be visible")
        
        # Check URLs
        from django.urls import reverse
        refund_url = reverse('refund_remainder')
        edit_url = reverse('edit_block_booking', args=[booking.id])
        
        print(f"✅ Refund URL: {refund_url}")
        print(f"✅ Edit URL: {edit_url}")
        
        print("✅ UI integration test passed!")
        
    except Exception as e:
        print(f"❌ UI integration test failed: {e}")

if __name__ == "__main__":
    print("Testing New Block Booking Features")
    print("=" * 60)
    
    test_refund_remainder()
    test_edit_block_booking_validation()
    test_ui_integration()
    
    print("\n" + "=" * 60)
    print("All tests completed!")
    print("\nTo test the web interface:")
    print("1. Go to http://127.0.0.1:8000/students/888/edit/")
    print("2. Look for the 'Actions' column in the block bookings table")
    print("3. Try the 'Refund' and 'Edit' buttons")
    print("4. Test the edit form validation with different values")
