# LTDRW Project

A Django 4.2 project with PostgreSQL integration.

## Setup

1. Create and activate virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate
```

2. Install dependencies:
```bash
pip install --break-system-packages -r requirements.txt
```

3. Run migrations:
```bash
python3 manage.py migrate
```

4. Run the development server:
```bash
python3 manage.py runserver
```

## Features
- Django 4.2
- PostgreSQL database integration
- Django Crispy Forms with Bootstrap 5
- JW app integration
