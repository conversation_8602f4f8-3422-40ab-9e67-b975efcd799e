from django.urls import path
from . import views

urlpatterns = [
    path('', views.dashboard, name='dashboard'),  # This will be the root/landing page
    path('stats/', views.stats_view, name='stats'),

    # Student URLs
    path('students/', views.student_list, name='student_list'),
    path('students/create/', views.student_create, name='student_create'),
    path('students/<int:pk>/', views.student_detail, name='student_detail'),
    path('students/<int:pk>/edit/', views.student_edit, name='student_edit'),
    path('students/<int:pk>/delete/', views.student_delete, name='student_delete'),
    path('students/<str:student_name>/lessons/', views.student_lessons, name='student_lessons'),

    # Lesson URLs
    path('lessons/', views.lesson_list, name='lesson_list'),
    path('lessons/create/', views.lesson_create, name='lesson_create'),
    path('lessons/<int:pk>/update/', views.lesson_update, name='lesson_update'),
    path('lessons/<int:pk>/delete/', views.lesson_delete, name='lesson_delete'),

    # Business Expense URLs
    path('business-expenses/', views.business_expense_list, name='business_expense_list'),
    path('business-expenses/create/', views.business_expense_create, name='business_expense_create'),
    path('business-expenses/<int:pk>/update/', views.business_expense_update, name='business_expense_update'),
    path('business-expenses/<int:pk>/delete/', views.business_expense_delete, name='business_expense_delete'),
    path('business-expenses/type/<str:expense_type>/', views.expense_type_details, name='expense_type_details'),

    # Mileage URLs
    path('mileage/', views.mileage_list, name='mileage_list'),
    path('mileage/create/', views.mileage_create, name='mileage_create'),
    path('mileage/<int:pk>/update/', views.mileage_update, name='mileage_update'),
    path('mileage/<int:pk>/delete/', views.mileage_delete, name='mileage_delete'),

    # Business Mileage URLs
    path('business-mileage/', views.business_mileage_list, name='business_mileage_list'),
    path('business-mileage/create/', views.business_mileage_create, name='business_mileage_create'),
    path('business-mileage/<int:pk>/update/', views.business_mileage_update, name='business_mileage_update'),
    path('business-mileage/<int:pk>/delete/', views.business_mileage_delete, name='business_mileage_delete'),

    # Personal Mileage URLs
    path('personal-mileage/', views.personal_mileage_list, name='personal_mileage_list'),
    path('personal-mileage/create/', views.personal_mileage_create, name='personal_mileage_create'),
    path('personal-mileage/<int:pk>/update/', views.personal_mileage_update, name='personal_mileage_update'),
    path('personal-mileage/<int:pk>/delete/', views.personal_mileage_delete, name='personal_mileage_delete'),

    # Fuel Expense URLs
    path('fuel-expenses/', views.fuel_expense_list, name='fuel_expense_list'),
    path('fuel-expenses/create/', views.fuel_expense_create, name='fuel_expense_create'),
    path('fuel-expenses/<int:pk>/update/', views.fuel_expense_update, name='fuel_expense_update'),
    path('fuel-expenses/<int:pk>/delete/', views.fuel_expense_delete, name='fuel_expense_delete'),

    # Report URLs
    path('reports/', views.report_list, name='report_list'),
    path('reports/monthly/', views.monthly_report, name='monthly_report'),
    path('reports/monthly/pdf/', views.monthly_report_pdf, name='monthly_report_pdf'),
    path('reports/monthly/summary/', views.monthly_report_summary, name='monthly_report_summary'),
    path('reports/monthly/summary/pdf/', views.monthly_report_summary_pdf, name='monthly_report_summary_pdf'),
    # Tax Year Reports
    path('reports/tax-year/', views.tax_year_report, name='tax_year_report'),
    path('reports/tax-year/pdf/', views.tax_year_report_pdf, name='tax_year_report_pdf'),
    path('reports/tax-year/summary/', views.tax_year_report_summary, name='tax_year_report_summary'),
    path('reports/tax-year/summary/pdf/', views.tax_year_report_summary_pdf, name='tax_year_report_summary_pdf'),

    # Report Header Management
    path('my-data/', views.manage_report_header, name='manage_report_header'),

    # API endpoints
    path('api/areas/', views.get_areas, name='get_areas'),
    path('add_block_booking/', views.add_block_booking, name='add_block_booking'),
    path('get_active_block_bookings/', views.get_active_block_bookings, name='get_active_block_bookings'),
    path('get_student_remainder/', views.get_student_remainder, name='get_student_remainder'),

    # Block Booking Management
    path('refund_remainder/', views.refund_remainder, name='refund_remainder'),
    path('block-booking/<int:booking_id>/edit/', views.edit_block_booking, name='edit_block_booking'),
]
