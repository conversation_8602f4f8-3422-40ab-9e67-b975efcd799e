{% extends 'JW/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ action }} Lesson{% endblock %}

{% block content %}
<style>
    /* Hide Day of Week field on screens smaller than xl */
    @media (max-width: 1199.98px) {
        #div_id_day_of_week {
            display: none;
        }
    }
</style>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <h2>{{ action }} Lesson</h2>
        <form method="post" class="row g-3">
            {% csrf_token %}
            {{ form|crispy }}

            <!-- Block Booking Information (conditionally displayed) -->
            <div id="blockBookingInfo" class="card mt-3" style="display: none;">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Block Booking Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Block Booking:</strong> <span id="blockBookingDate"></span></p>
                            <p><strong>Amount Paid:</strong> £<span id="blockBookingAmount"></span></p>
                            <p><strong>Price Per Lesson:</strong> £<span id="blockBookingPricePerLesson"></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Current Balance:</strong> <span id="currentLessonsRemaining"></span> lessons (£<span id="currentAmountRemaining"></span>)</p>
                            <!-- Hidden fields for calculations -->
                            <span id="thisLessonHours" style="display: none;">0.0</span>
                            <span id="newLessonsRemaining" style="display: none;">0.0</span>
                            <span id="newAmountRemaining" style="display: none;">0.00</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12 col-sm-6 col-xl">
                    <button type="submit" name="action" value="save" class="btn btn-primary w-100">Save</button>
                </div>
                <div class="col-12 col-sm-6 col-xl">
                    <button type="submit" name="action" value="save_and_add" class="btn btn-success w-100">Another</button>
                </div>
                <div class="col-12 col-sm-6 col-xl">
                    <button type="submit" name="action" value="passed" class="btn w-100"
                        style="background-color: #FFD700; color: #000000;">
                        Passed
                    </button>
                </div>
                <div class="col-12 col-sm-6 col-xl">
                    <button type="submit" name="action" value="failed" class="btn btn-danger w-100">Failed</button>
                </div>
            </div>
        </form>

        <!-- Datalist for student names -->
        <datalist id="student-list">
            {% for student in existing_students %}
            <option value="{{ student }}">
                {% endfor %}
        </datalist>
    </div>
</div>
{% endblock %}

{% block javascript %}
<script>
    (function () {
        // Get form elements
        const dateInput = document.querySelector('#id_date');
        const dayOfWeekInput = document.querySelector('#id_day_of_week');
        const lessonHoursInput = document.querySelector('#id_lesson_hours');
        const pricePerHourInput = document.querySelector('#id_price_per_hour');
        const studentNameInput = document.querySelector('#id_student_name');
        const amountInput = document.querySelector('#id_amount');
        const saveButton = document.querySelector('button[value="save"]');
        const anotherButton = document.querySelector('button[value="save_and_add"]');
        const passedButton = document.querySelector('button[value="passed"]');
        const failedButton = document.querySelector('button[value="failed"]');
        const blockBookingInfo = document.querySelector('#blockBookingInfo');

        // Block booking elements
        const blockBookingDate = document.querySelector('#blockBookingDate');
        const blockBookingAmount = document.querySelector('#blockBookingAmount');
        const blockBookingPricePerLesson = document.querySelector('#blockBookingPricePerLesson');
        const currentLessonsRemaining = document.querySelector('#currentLessonsRemaining');
        const currentAmountRemaining = document.querySelector('#currentAmountRemaining');
        const thisLessonHours = document.querySelector('#thisLessonHours');
        const newLessonsRemaining = document.querySelector('#newLessonsRemaining');
        const newAmountRemaining = document.querySelector('#newAmountRemaining');

        // Colors for different states
        const COLORS = {
            DEFAULT: 'white',
            EMPTY: '#fff5f5',     // Very light red for empty
            ZERO: '#ffcccc'       // Darker red for zero values
        };

        // Function to check if test-only entry
        function checkTestOnlyEntry() {
            const hours = lessonHoursInput.value.trim();
            const rate = pricePerHourInput.value.trim();

            // Always enable passed/failed buttons
            if (passedButton) passedButton.disabled = false;
            if (failedButton) failedButton.disabled = false;

            // Handle hours field
            if (hours === '') {
                lessonHoursInput.style.backgroundColor = COLORS.EMPTY;
            } else if (parseFloat(hours) === 0) {
                lessonHoursInput.style.backgroundColor = COLORS.ZERO;
            } else {
                lessonHoursInput.style.backgroundColor = COLORS.DEFAULT;
            }

            // Handle rate field
            if (rate === '') {
                pricePerHourInput.style.backgroundColor = COLORS.EMPTY;
            } else if (parseFloat(rate) === 0) {
                pricePerHourInput.style.backgroundColor = COLORS.ZERO;
            } else {
                pricePerHourInput.style.backgroundColor = COLORS.DEFAULT;
            }

            // Handle button states
            const shouldDisableButtons = hours === '' || rate === '' ||
                parseFloat(hours) === 0 || parseFloat(rate) === 0;

            if (saveButton) {
                saveButton.disabled = shouldDisableButtons;
                saveButton.style.opacity = shouldDisableButtons ? '0.5' : '1';
            }
            if (anotherButton) {
                anotherButton.disabled = shouldDisableButtons;
                anotherButton.style.opacity = shouldDisableButtons ? '0.5' : '1';
            }
        }

        // Set default date to today if it's a new form
        if (dateInput && !dateInput.value) {
            const today = new Date();
            const yyyy = today.getFullYear();
            const mm = String(today.getMonth() + 1).padStart(2, '0');
            const dd = String(today.getDate()).padStart(2, '0');
            dateInput.value = `${yyyy}-${mm}-${dd}`;
            updateDayOfWeek();
        }

        // Function to update day of week
        function updateDayOfWeek() {
            if (!dateInput.value) return;
            const date = new Date(dateInput.value);
            if (isNaN(date.getTime())) return;

            const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            dayOfWeekInput.value = days[date.getDay()];
        }

        // Function to calculate amount
        function calculateAmount() {
            const hours = parseFloat(lessonHoursInput.value) || 0;
            const rate = parseFloat(pricePerHourInput.value) || 0;
            amountInput.value = (hours * rate).toFixed(2);
        }

        // Function to check for block bookings and update the UI
        function checkBlockBookings() {
            const studentName = studentNameInput.value.trim();
            if (!studentName) {
                blockBookingInfo.style.display = 'none';
                return;
            }

            // Fetch block booking information for this student
            fetch(`/get_active_block_bookings/?student_name=${encodeURIComponent(studentName)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.block_booking) {
                        // We have an active block booking, show the info
                        const booking = data.block_booking;
                        blockBookingDate.textContent = new Date(booking.date_created).toLocaleDateString();
                        blockBookingAmount.textContent = booking.amount_paid.toFixed(2);
                        blockBookingPricePerLesson.textContent = booking.price_per_lesson.toFixed(2);
                        currentLessonsRemaining.textContent = booking.lessons_remaining.toFixed(1);
                        currentAmountRemaining.textContent = (booking.lessons_remaining * booking.price_per_lesson).toFixed(2);

                        // Set the price per hour to match the block booking price per lesson
                        pricePerHourInput.value = booking.price_per_lesson.toFixed(2);
                        pricePerHourInput.readOnly = true; // Make it read-only
                        pricePerHourInput.style.backgroundColor = '#f8f9fa'; // Light gray background to indicate it's read-only

                        // Initialize the lesson hours calculation
                        const hours = parseFloat(lessonHoursInput.value) || 0;
                        thisLessonHours.textContent = hours.toFixed(1);

                        // Calculate initial new balance
                        const currentRemaining = parseFloat(booking.lessons_remaining) || 0;
                        const pricePerLesson = parseFloat(booking.price_per_lesson) || 0;
                        const newRemaining = Math.max(0, currentRemaining - hours);
                        newLessonsRemaining.textContent = newRemaining.toFixed(1);
                        newAmountRemaining.textContent = (newRemaining * pricePerLesson).toFixed(2);

                        // Update lesson hours and calculate new balance
                        updateBlockBookingCalculations();

                        // Show the block booking info section
                        blockBookingInfo.style.display = 'block';
                    } else {
                        // No active block booking, hide the info
                        blockBookingInfo.style.display = 'none';
                        // Reset price per hour field to be editable
                        pricePerHourInput.readOnly = false;
                        pricePerHourInput.style.backgroundColor = '';
                    }
                })
                .catch(error => {
                    console.error('Error fetching block bookings:', error);
                    blockBookingInfo.style.display = 'none';
                });
        }

        // Function to update block booking calculations when lesson hours change
        function updateBlockBookingCalculations() {
            if (blockBookingInfo.style.display === 'none') return;

            const hours = parseFloat(lessonHoursInput.value) || 0;
            const currentRemaining = parseFloat(currentLessonsRemaining.textContent) || 0;
            const pricePerLesson = parseFloat(blockBookingPricePerLesson.textContent) || 0;

            // Update this lesson hours
            thisLessonHours.textContent = hours.toFixed(1);

            // Calculate new remaining balance
            const newRemaining = Math.max(0, currentRemaining - hours);
            newLessonsRemaining.textContent = newRemaining.toFixed(1);
            newAmountRemaining.textContent = (newRemaining * pricePerLesson).toFixed(2);

            // We don't need to update the display since the fields are now hidden
            // Just update the hidden field values for calculations

            // Update price per hour to match block booking price per lesson
            if (pricePerLesson > 0) {
                pricePerHourInput.value = pricePerLesson.toFixed(2);
            }

            // Calculate amount
            calculateAmount();
        }

        // Add event listeners
        if (dateInput) {
            dateInput.addEventListener('input', updateDayOfWeek);
            dateInput.addEventListener('change', updateDayOfWeek);
        }

        if (lessonHoursInput) {
            lessonHoursInput.addEventListener('input', function () {
                checkTestOnlyEntry();
                calculateAmount();
                updateBlockBookingCalculations();
            });
            lessonHoursInput.addEventListener('change', function () {
                checkTestOnlyEntry();
                calculateAmount();
                updateBlockBookingCalculations();
            });
            // Trigger calculation on page load if there's a value
            setTimeout(function() {
                updateBlockBookingCalculations();
            }, 200);
        }

        if (pricePerHourInput) {
            pricePerHourInput.addEventListener('input', function () {
                checkTestOnlyEntry();
                calculateAmount();
            });
            pricePerHourInput.addEventListener('change', function () {
                checkTestOnlyEntry();
                calculateAmount();
            });
        }

        if (studentNameInput) {
            studentNameInput.addEventListener('change', checkBlockBookings);
            // Also check on page load if student name is already filled
            if (studentNameInput.value.trim()) {
                // Delay slightly to ensure all elements are properly initialized
                setTimeout(checkBlockBookings, 100);
            }
        }

        // Add event listeners for immediate feedback
        lessonHoursInput.addEventListener('input', checkTestOnlyEntry);
        pricePerHourInput.addEventListener('input', checkTestOnlyEntry);

        // Initial check
        checkTestOnlyEntry();

        // Add event listeners for the Passed/Failed buttons
        if (passedButton) {
            passedButton.addEventListener('click', function () {
                // Hide block booking info for test results
                blockBookingInfo.style.display = 'none';
            });
        }
        if (failedButton) {
            failedButton.addEventListener('click', function () {
                // Hide block booking info for test results
                blockBookingInfo.style.display = 'none';
            });
        }
    })();
</script>
{% endblock %}