{% extends 'JW/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<style>
    .form-group {
        margin-bottom: 1rem;
    }

    textarea.form-control {
        min-height: 80px;
    }
</style>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <h2>{{ title }}</h2>
        <form method="post" id="studentForm" action="{% if return_url %}?return_url={{ return_url }}{% endif %}">
            {% csrf_token %}
            {% if return_url %}
            <input type="hidden" name="return_url" value="{{ return_url }}">
            {% endif %}

            <div class="row g-2">
                <div class="col-12">
                    {{ form.student_name|as_crispy_field }}
                </div>
            </div>
            <div class="row g-2">
                <div class="col-12 col-sm-6 col-xl-4">
                    {{ form.gender|as_crispy_field }}
                </div>
                <div class="col-12 col-sm-6 col-xl-4">
                    {{ form.age|as_crispy_field }}
                </div>
                <div class="col-12 col-xl-4">
                    {{ form.active|as_crispy_field }}
                </div>
            </div>
            <div class="row g-2">
                <div class="col-12 col-sm-6">
                    {{ form.email_address|as_crispy_field }}
                </div>
                <div class="col-12 col-sm-6">
                    {{ form.mobile_number|as_crispy_field }}
                </div>
            </div>
            <div class="row g-2">
                <div class="col-12 col-sm-6">
                    {{ form.area|as_crispy_field }}
                </div>
                <div class="col-12 col-sm-6">
                    {{ form.post_code|as_crispy_field }}
                </div>
            </div>
            {{ form.address_1st_line|as_crispy_field }}
            {{ form.address_2nd_line|as_crispy_field }}
            {{ form.notes|as_crispy_field }}

            <!-- Block Booking Section -->
            <div class="mt-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h4>Block Bookings</h4>
                    <button type="button" id="addBlockBookingBtn" class="btn btn-sm btn-primary" {% if has_active_block_booking %}disabled title="This student already has an active block booking with remaining credit"{% endif %}>Add Block Booking</button>
                </div>

                <!-- Block Booking Form (initially hidden) -->
                <div id="blockBookingForm" class="card mb-3" style="display: none;">
                    <div class="card-body">
                        <h5 class="card-title">New Block Booking</h5>
                        <div class="row g-2">
                            <div class="col-md-6">
                                <label for="id_date_created" class="form-label">Date</label>
                                <input type="date" id="id_date_created" name="date_created" class="form-control" value="{{ today_date|date:'Y-m-d' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="id_amount_paid" class="form-label">Amount Paid (£)</label>
                                <input type="number" id="id_amount_paid" name="amount_paid" class="form-control" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="row g-2 mt-2">
                            <div class="col-md-6">
                                <label for="id_total_lessons" class="form-label">Number of Lessons</label>
                                <input type="number" id="id_total_lessons" name="total_lessons" class="form-control" step="0.5" min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="id_price_per_lesson_fixed" class="form-label">Price per Lesson (£)</label>
                                <input type="number" id="id_price_per_lesson_fixed" name="price_per_lesson_fixed" class="form-control" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="row g-2 mt-2">
                            <div class="col-md-6">
                                <label for="id_previous_remainder" class="form-label">Previous Remainder (£)</label>
                                <input type="number" id="id_previous_remainder" class="form-control" step="0.01" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div class="col-md-6">
                                <label for="id_new_remainder" class="form-label">New Remainder (£)</label>
                                <input type="number" id="id_new_remainder" name="remainder_balance" class="form-control" step="0.01" readonly style="background-color: #f8f9fa;">
                            </div>
                        </div>
                        <div class="row g-2 mt-2">
                            <div class="col-12">
                                <label for="id_block_booking_notes" class="form-label">Notes</label>
                                <textarea id="id_block_booking_notes" name="block_booking_notes" class="form-control" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="button" id="cancelBlockBookingBtn" class="btn btn-secondary">Cancel</button>
                            <!-- Hidden fields to store block booking data -->
                            <input type="hidden" id="has_block_booking" name="has_block_booking" value="false">
                            <input type="hidden" id="calculation_method" name="calculation_method" value="new">
                            <input type="hidden" id="block_booking_date" name="block_booking_date" value="">
                            <input type="hidden" id="block_booking_amount" name="block_booking_amount" value="">
                            <input type="hidden" id="block_booking_lessons" name="block_booking_lessons" value="">
                            <input type="hidden" id="block_booking_notes" name="block_booking_notes" value="">
                        </div>
                    </div>
                </div>

                <!-- Existing Block Bookings -->
                {% if block_bookings %}
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Amount Paid</th>
                                <th>Total Lessons</th>
                                <th>Lessons Used</th>
                                <th>Lessons Remaining</th>
                                <th>Price/Lesson</th>
                                <th>Remainder</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in block_bookings %}
                            <tr>
                                <td>{{ booking.date_created|date:"M d, Y" }}</td>
                                <td>£{{ booking.amount_paid|floatformat:2 }}</td>
                                <td>{{ booking.total_lessons|floatformat:1 }}</td>
                                <td>{{ booking.lessons_used|floatformat:1 }}</td>
                                <td>{{ booking.lessons_remaining|floatformat:1 }}</td>
                                <td>£{{ booking.price_per_lesson|floatformat:2 }}</td>
                                <td>£{{ booking.remainder_balance|floatformat:2 }}</td>
                                <td>{% if booking.is_fully_used %}<span class="badge bg-secondary">Used</span>{% else %}<span class="badge bg-success">Active</span>{% endif %}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        {% if booking.remainder_balance > 0 %}
                                        <button type="button" class="btn btn-outline-warning btn-sm refund-remainder-btn"
                                                data-booking-id="{{ booking.id }}"
                                                data-remainder="{{ booking.remainder_balance|floatformat:2 }}"
                                                title="Refund remainder to student">
                                            <i class="fas fa-money-bill-wave"></i> Refund £{{ booking.remainder_balance|floatformat:2 }}
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-primary btn-sm edit-booking-btn"
                                                data-booking-id="{{ booking.id }}"
                                                title="Edit this block booking">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No block bookings found for this student.</p>
                {% endif %}
            </div>

            <div class="mt-3">
                <div class="row g-2">
                    <!-- Primary action button -->
                    <div class="col-12 col-sm-6">
                        <button type="submit" name="action" value="save" class="btn btn-primary w-100">Save</button>
                    </div>

                    <!-- Create & Add Another (only shown on Create) -->
                    {% if title == 'Add Student' %}
                    <div class="col-12 col-sm-6">
                        <button type="submit" name="action" value="save_and_add" class="btn btn-success w-100">Add Another</button>
                    </div>
                    {% endif %}
                </div>
                <!-- Cancel button -->
                <div class="row mt-2">
                    <div class="col-12">
                        {% if return_url %}
                        <a href="{{ return_url }}" class="btn btn-secondary w-100">Cancel</a>
                        {% else %}
                        <a href="{% url 'student_list' %}" class="btn btn-secondary w-100">Cancel</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.btn-group-sm .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.refund-remainder-btn {
    white-space: nowrap;
}

.edit-booking-btn {
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set up area field with datalist
    const areaField = document.querySelector('input[name="area"]');
    if (areaField) {
        areaField.setAttribute('list', 'area-list');
    }

    // Fetch areas for datalist
    fetch('{% url "get_areas" %}')
        .then(response => response.json())
        .then(areas => {
            const datalist = document.getElementById('area-list');
            areas.forEach(area => {
                const option = document.createElement('option');
                option.value = area;
                datalist.appendChild(option);
            });
        });

    // Block Booking Form Functionality
    const addBlockBookingBtn = document.getElementById('addBlockBookingBtn');
    const blockBookingForm = document.getElementById('blockBookingForm');
    const cancelBlockBookingBtn = document.getElementById('cancelBlockBookingBtn');
    const amountPaidInput = document.getElementById('id_amount_paid');
    const totalLessonsInput = document.getElementById('id_total_lessons');
    const pricePerLessonFixedInput = document.getElementById('id_price_per_lesson_fixed');
    const previousRemainderInput = document.getElementById('id_previous_remainder');
    const newRemainderInput = document.getElementById('id_new_remainder');

    // Hidden fields for block booking data
    const hasBlockBookingInput = document.getElementById('has_block_booking');
    const blockBookingDateInput = document.getElementById('block_booking_date');
    const blockBookingAmountInput = document.getElementById('block_booking_amount');
    const blockBookingLessonsInput = document.getElementById('block_booking_lessons');
    const blockBookingNotesInput = document.getElementById('block_booking_notes');

    // Set today's date as default
    const today = new Date();
    const dateInput = document.getElementById('id_date_created');
    if (dateInput && !dateInput.value) {
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        dateInput.value = `${yyyy}-${mm}-${dd}`;
    }

    // Show/hide block booking form
    if (addBlockBookingBtn) {
        addBlockBookingBtn.addEventListener('click', function() {
            blockBookingForm.style.display = 'block';
            addBlockBookingBtn.style.display = 'none';
            // Set has_block_booking to true
            hasBlockBookingInput.value = 'true';
            // Load previous remainder for this student
            loadPreviousRemainder();
        });
    }

    if (cancelBlockBookingBtn) {
        cancelBlockBookingBtn.addEventListener('click', function() {
            blockBookingForm.style.display = 'none';
            addBlockBookingBtn.style.display = 'inline-block';
            // Reset form and hidden fields
            amountPaidInput.value = '';
            totalLessonsInput.value = '';
            pricePerLessonFixedInput.value = '';
            previousRemainderInput.value = '';
            newRemainderInput.value = '';
            document.getElementById('id_block_booking_notes').value = '';
            hasBlockBookingInput.value = 'false';
            blockBookingDateInput.value = '';
            blockBookingAmountInput.value = '';
            blockBookingLessonsInput.value = '';
            blockBookingNotesInput.value = '';
        });
    }

    // Update hidden fields when block booking form fields change
    if (dateInput) {
        dateInput.addEventListener('change', function() {
            blockBookingDateInput.value = this.value;
        });
    }

    if (amountPaidInput) {
        amountPaidInput.addEventListener('input', function() {
            blockBookingAmountInput.value = this.value;
        });
    }

    if (totalLessonsInput) {
        totalLessonsInput.addEventListener('input', function() {
            blockBookingLessonsInput.value = this.value;
        });
    }

    const blockBookingNotesTextarea = document.getElementById('id_block_booking_notes');
    if (blockBookingNotesTextarea) {
        blockBookingNotesTextarea.addEventListener('input', function() {
            blockBookingNotesInput.value = this.value;
        });
    }

    // Calculate remainder balance
    function calculateRemainder() {
        const amountPaid = parseFloat(amountPaidInput.value) || 0;
        const totalLessons = parseFloat(totalLessonsInput.value) || 0;
        const pricePerLesson = parseFloat(pricePerLessonFixedInput.value) || 0;
        const previousRemainder = parseFloat(previousRemainderInput.value) || 0;

        if (amountPaid > 0 && totalLessons > 0 && pricePerLesson > 0) {
            const totalCost = totalLessons * pricePerLesson;
            const totalAvailable = amountPaid + previousRemainder;
            const newRemainder = totalAvailable - totalCost;
            newRemainderInput.value = newRemainder.toFixed(2);
        } else {
            newRemainderInput.value = '';
        }
    }

    // Function to load previous remainder for the student
    function loadPreviousRemainder() {
        const studentNameInput = document.querySelector('input[name="student_name"]');
        if (!studentNameInput || !studentNameInput.value.trim()) {
            previousRemainderInput.value = '0.00';
            return;
        }

        // Fetch the student's last block booking remainder
        fetch(`/get_student_remainder/?student_name=${encodeURIComponent(studentNameInput.value.trim())}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    previousRemainderInput.value = data.remainder.toFixed(2);
                    calculateRemainder(); // Recalculate with new remainder
                } else {
                    previousRemainderInput.value = '0.00';
                }
            })
            .catch(error => {
                console.error('Error fetching student remainder:', error);
                previousRemainderInput.value = '0.00';
            });
    }

    if (amountPaidInput && totalLessonsInput && pricePerLessonFixedInput) {
        amountPaidInput.addEventListener('input', calculateRemainder);
        totalLessonsInput.addEventListener('input', calculateRemainder);
        pricePerLessonFixedInput.addEventListener('input', calculateRemainder);
    }

    // Validate block booking fields before form submission
    document.getElementById('studentForm').addEventListener('submit', function(event) {
        // Only validate if block booking is enabled
        if (hasBlockBookingInput.value === 'true') {
            const dateCreated = dateInput.value;
            const amountPaid = amountPaidInput.value;
            const totalLessons = totalLessonsInput.value;
            const pricePerLesson = pricePerLessonFixedInput.value;

            // Update hidden fields with current values
            blockBookingDateInput.value = dateCreated;
            blockBookingAmountInput.value = amountPaid;
            blockBookingLessonsInput.value = totalLessons;
            blockBookingNotesInput.value = document.getElementById('id_block_booking_notes').value;

            // Validate inputs
            if (!dateCreated || !amountPaid || !totalLessons || !pricePerLesson) {
                alert('Please fill in all required block booking fields (Date, Amount Paid, Number of Lessons, and Price per Lesson)');
                event.preventDefault();
                return false;
            }

            // Validate that price per lesson is positive
            if (parseFloat(pricePerLesson) <= 0) {
                alert('Price per lesson must be greater than 0');
                event.preventDefault();
                return false;
            }
        }
    });

    // Refund Remainder Button Functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.refund-remainder-btn')) {
            const btn = e.target.closest('.refund-remainder-btn');
            const bookingId = btn.dataset.bookingId;
            const remainderAmount = btn.dataset.remainder;

            if (confirm(`Are you sure you want to refund £${remainderAmount} remainder to the student? This action cannot be undone.`)) {
                // Send AJAX request to refund remainder
                fetch('{% url "refund_remainder" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        booking_id: bookingId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Remainder refunded successfully!');
                        location.reload(); // Refresh the page to show updated values
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while processing the refund.');
                });
            }
        }

        if (e.target.closest('.edit-booking-btn')) {
            const btn = e.target.closest('.edit-booking-btn');
            const bookingId = btn.dataset.bookingId;

            // Redirect to edit block booking page
            window.location.href = `{% url "edit_block_booking" 0 %}`.replace('0', bookingId);
        }
    });
});
</script>
{% endblock %}
