#!/usr/bin/env python
"""
Test the new button functionality in the web interface
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking
from decimal import Decimal

def test_button_visibility():
    """Test that buttons should be visible for the right conditions"""
    print("Testing Button Visibility Logic")
    print("=" * 40)
    
    # Get <PERSON> Fortune's booking
    student = AppJwStudent.objects.get(pk=879)
    booking = AppJwBlockBooking.objects.get(pk=34)
    
    print(f"Student: {student.student_name}")
    print(f"Booking ID: {booking.id}")
    print(f"Remainder: £{booking.remainder_balance}")
    
    # Test refund button visibility
    if booking.remainder_balance > 0:
        print("✅ Refund button SHOULD be visible (remainder > 0)")
    else:
        print("❌ Refund button should NOT be visible (no remainder)")
    
    # Edit button should always be visible
    print("✅ Edit button SHOULD always be visible")
    
    print(f"\nExpected button HTML:")
    if booking.remainder_balance > 0:
        print(f'<button class="refund-remainder-btn" data-booking-id="{booking.id}">Refund £{booking.remainder_balance:.2f}</button>')
    print(f'<button class="edit-booking-btn" data-booking-id="{booking.id}">Edit</button>')

def test_urls():
    """Test that the URLs are working"""
    print("\nTesting URLs")
    print("=" * 40)
    
    from django.urls import reverse
    
    try:
        refund_url = reverse('refund_remainder')
        print(f"✅ Refund URL: {refund_url}")
    except Exception as e:
        print(f"❌ Refund URL error: {e}")
    
    try:
        edit_url = reverse('edit_block_booking', args=[34])
        print(f"✅ Edit URL: {edit_url}")
    except Exception as e:
        print(f"❌ Edit URL error: {e}")

def test_data_integrity():
    """Test that the data is correct for the buttons"""
    print("\nTesting Data Integrity")
    print("=" * 40)
    
    booking = AppJwBlockBooking.objects.get(pk=34)
    
    print(f"Booking data for buttons:")
    print(f"  ID: {booking.id}")
    print(f"  Remainder: £{booking.remainder_balance}")
    print(f"  Active: {booking.active}")
    print(f"  Student: {booking.student.student_name}")
    
    # Verify the data makes sense
    if booking.remainder_balance >= 0:
        print("✅ Remainder balance is valid")
    else:
        print("❌ Remainder balance is negative!")
    
    if booking.active:
        print("✅ Booking is active")
    else:
        print("⚠️ Booking is inactive")

if __name__ == "__main__":
    print("Testing Button Functionality")
    print("=" * 50)
    
    test_button_visibility()
    test_urls()
    test_data_integrity()
    
    print("\n" + "=" * 50)
    print("Manual Testing Instructions:")
    print("1. Go to: http://127.0.0.1:8000/students/879/edit/")
    print("2. Scroll down to 'Existing Block Bookings' section")
    print("3. Look for 'Actions' column in the table")
    print("4. You should see:")
    print("   - 'Refund £30.00' button (yellow/warning)")
    print("   - 'Edit' button (blue/primary)")
    print("5. Click 'Refund £30.00' to test refund functionality")
    print("6. Click 'Edit' to test edit functionality")
    print("\nIf buttons are not visible, check:")
    print("- Browser console for JavaScript errors")
    print("- HTML source to see if buttons are rendered")
    print("- CSS styles that might be hiding the buttons")
