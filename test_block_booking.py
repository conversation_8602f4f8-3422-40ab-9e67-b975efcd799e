#!/usr/bin/env python
"""
Test script for the enhanced block booking system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking
from decimal import Decimal
from django.db.models import Sum

def test_block_booking_calculations():
    """Test the new block booking calculation system"""
    print("Testing Enhanced Block Booking System")
    print("=" * 50)

    # Test 1: Check <PERSON>'s block bookings
    print("\nTest 1: <PERSON>'s current block bookings")
    print("-" * 40)

    try:
        mark_smith = AppJwStudent.objects.get(student_name="<PERSON>")
        print(f"Found student: {mark_smith.student_name}")

        bookings = AppJwBlockBooking.objects.filter(student=mark_smith).order_by('-date_created')
        print(f"Total bookings: {bookings.count()}")

        for i, booking in enumerate(bookings, 1):
            print(f"\nBooking {i}:")
            print(f"  Date: {booking.date_created}")
            print(f"  Amount paid: £{booking.amount_paid}")
            print(f"  Total lessons: {booking.total_lessons}")
            print(f"  Lessons used: {booking.lessons_used}")
            print(f"  Price per lesson (fixed): £{booking.price_per_lesson_fixed or 'N/A'}")
            print(f"  Remainder balance: £{booking.remainder_balance}")
            print(f"  Calculation method: {booking.calculation_method}")
            print(f"  Total value remaining: £{booking.total_value_remaining}")
            print(f"  Active: {booking.active}")

        # Test 2: Simulate the correct scenario
        print(f"\nTest 2: Simulating correct block booking scenario")
        print("-" * 40)

        # Clear existing bookings for clean test
        AppJwBlockBooking.objects.filter(student=mark_smith).delete()

        # Scenario 1: First booking - £40 paid, 1 lesson at £35 = £5 remainder
        booking1 = AppJwBlockBooking.objects.create(
            student=mark_smith,
            date_created='2025-01-31',
            amount_paid=Decimal('40.00'),
            total_lessons=Decimal('1.0'),
            price_per_lesson_fixed=Decimal('35.00'),
            remainder_balance=Decimal('5.00'),  # £40 - (1 × £35) = £5
            calculation_method='new',
            active=True
        )
        print(f"Created booking 1: £40 paid, 1 lesson at £35, remainder: £{booking1.remainder_balance}")

        # Simulate lesson usage - should consume £35 from the £40, leaving £5 remainder
        success = booking1.add_usage(1.0, 35.00)  # 1 hour at £35/hour
        print(f"Added 1 hour lesson at £35: Success={success}")
        print(f"After lesson - Lessons used: {booking1.lessons_used}, Remainder: £{booking1.remainder_balance}")
        print(f"Total value remaining: £{booking1.total_value_remaining}")

        # Scenario 2: Second booking - £40 paid, should add previous £5 remainder
        booking2 = AppJwBlockBooking.objects.create(
            student=mark_smith,
            date_created='2025-01-31',
            amount_paid=Decimal('40.00'),
            total_lessons=Decimal('1.0'),
            price_per_lesson_fixed=Decimal('35.00'),
            remainder_balance=Decimal('10.00'),  # £40 + £5 previous - (1 × £35) = £10
            calculation_method='new',
            active=True
        )
        print(f"\nCreated booking 2: £40 paid + £5 previous remainder, 1 lesson at £35")
        print(f"Expected remainder: £10 (£40 + £5 - £35)")
        print(f"Actual remainder: £{booking2.remainder_balance}")

        # Test total remainder calculation
        total_remainder = AppJwBlockBooking.objects.filter(
            student=mark_smith,
            active=True
        ).aggregate(total=Sum('remainder_balance'))['total'] or Decimal('0.00')
        print(f"Total remainder from all active bookings: £{total_remainder}")

    except AppJwStudent.DoesNotExist:
        print("Mark Smith not found in database")
    except Exception as e:
        print(f"Error in test: {e}")

    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_block_booking_calculations()
