#!/usr/bin/env python
"""
Test script for the enhanced block booking system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking
from decimal import Decimal

def test_block_booking_calculations():
    """Test the new block booking calculation system"""
    print("Testing Enhanced Block Booking System")
    print("=" * 50)
    
    # Test 1: Create a new block booking with the new method
    print("\nTest 1: New calculation method")
    print("-" * 30)
    
    # Get a test student (or create one)
    try:
        student = AppJwStudent.objects.first()
        if not student:
            print("No students found in database. Please create a student first.")
            return
        
        print(f"Using student: {student.student_name}")
        
        # Create a new block booking with the new method
        booking = AppJwBlockBooking(
            student=student,
            date_created='2025-01-31',
            amount_paid=Decimal('100.00'),
            total_lessons=Decimal('4.0'),
            price_per_lesson_fixed=Decimal('20.00'),
            remainder_balance=Decimal('20.00'),  # £100 paid - (4 lessons × £20) = £20 remainder
            calculation_method='new'
        )
        
        print(f"Amount paid: £{booking.amount_paid}")
        print(f"Total lessons: {booking.total_lessons}")
        print(f"Price per lesson (fixed): £{booking.price_per_lesson_fixed}")
        print(f"Remainder balance: £{booking.remainder_balance}")
        print(f"Calculation method: {booking.calculation_method}")
        
        # Test the properties
        print(f"Price per lesson (calculated): £{booking.price_per_lesson}")
        print(f"Total value remaining: £{booking.total_value_remaining}")
        print(f"Effective lessons remaining: {booking.effective_lessons_remaining}")
        
    except Exception as e:
        print(f"Error in test: {e}")
    
    # Test 2: Check existing legacy bookings
    print("\nTest 2: Legacy calculation method")
    print("-" * 30)
    
    legacy_booking = AppJwBlockBooking.objects.filter(calculation_method='legacy').first()
    if legacy_booking:
        print(f"Legacy booking found:")
        print(f"Amount paid: £{legacy_booking.amount_paid}")
        print(f"Total lessons: {legacy_booking.total_lessons}")
        print(f"Price per lesson (calculated): £{legacy_booking.price_per_lesson}")
        print(f"Remainder balance: £{legacy_booking.remainder_balance}")
        print(f"Calculation method: {legacy_booking.calculation_method}")
    else:
        print("No legacy bookings found")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_block_booking_calculations()
