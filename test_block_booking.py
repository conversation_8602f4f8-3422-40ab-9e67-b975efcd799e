#!/usr/bin/env python
"""
Test script for the enhanced block booking system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LTDRW.settings')
django.setup()

from JW.models import AppJwStudent, AppJwBlockBooking
from decimal import Decimal

def test_block_booking_calculations():
    """Test the new block booking calculation system"""
    print("Testing Enhanced Block Booking System")
    print("=" * 50)

    # Test 1: Check <PERSON>'s block bookings
    print("\nTest 1: <PERSON>'s block bookings")
    print("-" * 30)

    try:
        mark_smith = AppJwStudent.objects.get(student_name="<PERSON>")
        print(f"Found student: {mark_smith.student_name}")

        bookings = AppJwBlockBooking.objects.filter(student=mark_smith).order_by('-date_created')
        print(f"Total bookings: {bookings.count()}")

        for i, booking in enumerate(bookings, 1):
            print(f"\nBooking {i}:")
            print(f"  Date: {booking.date_created}")
            print(f"  Amount paid: £{booking.amount_paid}")
            print(f"  Total lessons: {booking.total_lessons}")
            print(f"  Lessons used: {booking.lessons_used}")
            print(f"  Price per lesson (fixed): £{booking.price_per_lesson_fixed or 'N/A'}")
            print(f"  Remainder balance: £{booking.remainder_balance}")
            print(f"  Calculation method: {booking.calculation_method}")
            print(f"  Total value remaining: £{booking.total_value_remaining}")
            print(f"  Active: {booking.active}")

        # Test active booking selection logic (updated to match new logic)
        print(f"\nActive booking selection:")
        potential_bookings = AppJwBlockBooking.objects.filter(
            student=mark_smith,
            active=True
        ).order_by('-date_created')

        active_booking = None
        max_credit = 0
        for booking in potential_bookings:
            print(f"  Checking booking from {booking.date_created}: £{booking.total_value_remaining:.2f} remaining")
            if booking.total_value_remaining > max_credit:
                max_credit = booking.total_value_remaining
                active_booking = booking
                print(f"  -> New best booking with £{max_credit:.2f}!")

        if active_booking:
            print(f"Selected active booking: {active_booking.date_created} with £{active_booking.total_value_remaining:.2f} remaining")
        else:
            print("No active booking with credit found")

    except AppJwStudent.DoesNotExist:
        print("Mark Smith not found in database")
    except Exception as e:
        print(f"Error in test: {e}")

    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_block_booking_calculations()
